import { motion } from "framer-motion";
import React from "react";

interface ConnectingLineProps {
  height?: number;
  width?: number;
}

export const ConnectingLine: React.FC<ConnectingLineProps> = ({
  height = 100,
  width = 100
}) => {
  return (
    <div className="flex justify-center w-full">
      <svg
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        preserveAspectRatio="xMidYMid meet"
      >
        <defs>
          <mask id="outputLineMask">
            <path
              d={`M${width/2},0 L${width/2},${height}`}
              stroke="white"
              strokeWidth="2"
              fill="none"
            />
          </mask>
          <linearGradient id="outputGradient" x1="0" x2="0" y1="0" y2="1">
            <motion.stop
              stopColor="rgba(0,0,0,0.5)"
              animate={{ offset: ["-150%", "100%"] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.stop
              stopColor="transparent"
              animate={{ offset: ["-20%", "100%"] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.stop
              stopColor="aquamarine"
              animate={{ offset: ["-12%", "108%"] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.stop
              stopColor="rgba(0,0,0,0.5)"
              animate={{ offset: ["-8%", "112%"] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
          </linearGradient>
        </defs>
        <g mask="url(#outputLineMask)">
          <rect x="0" y="0" width={width} height={height} fill="#333" />
          <rect x="0" y="0" width={width} height={height} fill="url(#outputGradient)" />
        </g>
      </svg>
    </div>
  );
};
