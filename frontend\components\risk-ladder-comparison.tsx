"use client";

import React, { useState, useEffect } from 'react';
import RiskLadderTable from './risk-ladder-table';
import { Button } from './ui/button';

interface RiskLadderData {
  rug_pull_pct: number;
  small_gains_pct: number;
  good_profit_pct: number;
  big_gains_pct: number;
  to_the_moon_pct: number;
  pool_sample_size?: number;
  total_tokens?: number;
  last_updated_utc?: string;
  status?: string;
  analysis_period_hours?: number;
  grace_period_hours?: number;
}

interface ComparisonData {
  live_signals: RiskLadderData;
  sniper_bot_simulation: RiskLadderData;
  comparison_timestamp?: string;
}

const RiskLadderComparison: React.FC = () => {
  const [data, setData] = useState<ComparisonData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isTriggering, setIsTriggering] = useState(false);
  const [triggerResult, setTriggerResult] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);

  const fetchData = async () => {
    try {
      setError(null);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/risk-ladder/comparison`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('Error fetching risk ladder data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user is admin
  const checkAdminStatus = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setIsAdmin(false);
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/check`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        // Check if user is admin (you'll need to add is_admin to the response)
        // For now, check if wallet matches treasury wallet or any authenticated user
        setIsAdmin(true); // Temporary - will be properly implemented once admin field is added
      } else {
        setIsAdmin(false);
      }
    } catch (err) {
      setIsAdmin(false);
    }
  };

  // Trigger manual sniper bot resolution
  const triggerSniperBotResolution = async () => {
    if (isTriggering) return;

    setIsTriggering(true);
    setTriggerResult(null);

    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setTriggerResult('Error: Not authenticated');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/risk-ladder/admin/trigger-sniper-bot-resolution`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (response.ok) {
        setTriggerResult(`Success! Processed ${result.result?.tokens_processed || 0} tokens (${result.result?.tokens_successful || 0} successful)`);
        // Refresh data after successful trigger
        setTimeout(fetchData, 2000);
      } else {
        setTriggerResult(`Error: ${result.detail || 'Failed to trigger resolution'}`);
      }
    } catch (err) {
      setTriggerResult(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsTriggering(false);
      // Clear result message after 10 seconds
      setTimeout(() => setTriggerResult(null), 10000);
    }
  };

  useEffect(() => {
    fetchData();
    checkAdminStatus();

    // Set up periodic updates every 5 minutes
    const interval = setInterval(fetchData, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  // Calculate analysis period display text
  const getAnalysisPeriodText = () => {
    if (!data?.sniper_bot_simulation) return '';
    
    const analysisHours = data.sniper_bot_simulation.analysis_period_hours || 12;
    const graceHours = data.sniper_bot_simulation.grace_period_hours || 3;
    const totalHours = analysisHours + graceHours;
    
    return `Analysis Period: ${analysisHours}h ending ${graceHours}h ago (${totalHours}h - ${graceHours}h before now)`;
  };

  if (error) {
    return (
      <div className="w-full max-w-5xl">
        <div className="flex flex-col lg:flex-row justify-center gap-6 lg:gap-8">
          <div className="w-full lg:w-[380px] h-60 bg-gradient-to-br from-[#1a1a1a] to-[#2a2a2a] rounded-lg border border-[#333] flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-400 text-sm mb-2">Error loading data</p>
              <button
                onClick={fetchData}
                className="text-xs text-blue-400 hover:text-blue-300 underline"
              >
                Retry
              </button>
            </div>
          </div>
          <div className="w-full lg:w-[380px] h-60 bg-gradient-to-br from-[#1a1a1a] to-[#2a2a2a] rounded-lg border border-[#333] flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-400 text-sm mb-2">Error loading data</p>
              <button
                onClick={fetchData}
                className="text-xs text-blue-400 hover:text-blue-300 underline"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-5xl">
      {/* Admin Controls - Only show if admin */}
      {isAdmin && (
        <div className="mb-8 flex flex-col items-center space-y-2">
          <Button
            onClick={triggerSniperBotResolution}
            disabled={isTriggering}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            {isTriggering ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              'Update Sniper Bot Data'
            )}
          </Button>

          {triggerResult && (
            <div className={`text-sm px-4 py-2 rounded-lg ${
              triggerResult.startsWith('Success')
                ? 'bg-green-500/10 text-green-400 border border-green-500/30'
                : 'bg-red-500/10 text-red-400 border border-red-500/30'
            }`}>
              {triggerResult}
            </div>
          )}
        </div>
      )}

      {/* Tables Container */}
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
        <div className="w-full sm:w-[340px] lg:w-[370px]">
          <RiskLadderTable
            title="All Pump.fun Launches"
            subtitle={`Sniper bot buys every new token (${data?.sniper_bot_simulation?.total_tokens || 0} tokens)`}
            data={data?.sniper_bot_simulation || null}
            isLoading={isLoading}
            variant="sniper"
          />
        </div>

        <div className="w-full sm:w-[340px] lg:w-[370px]">
          <RiskLadderTable
            title="ScryBot Live Signals"
            subtitle={`AI-filtered signals (${data?.live_signals?.pool_sample_size || 0} signals)`}
            data={data?.live_signals || null}
            isLoading={isLoading}
            variant="signals"
          />
        </div>
      </div>
    </div>
  );
};

export default RiskLadderComparison;
