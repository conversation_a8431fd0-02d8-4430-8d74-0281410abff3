import React from 'react';

export const BuySignalIcon: React.FC = () => {
  return (
    <div className="buy-signal-icon">
      <div className="arrow-up"></div>
      <div className="buy-text">BUY<br />SIGNAL</div>
      <div className="signal-dot"></div>
      
      <style jsx>{`
        .buy-signal-icon {
          width: 80px;
          height: 80px;
          border: 3px solid #00ff88;
          border-radius: 12px;
          background: rgba(0, 255, 136, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          animation: pulse 2s infinite;
          box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .arrow-up {
          width: 0;
          height: 0;
          border-left: 15px solid transparent;
          border-right: 15px solid transparent;
          border-bottom: 20px solid #00ff88;
          margin-bottom: 5px;
        }

        .buy-text {
          position: absolute;
          bottom: 8px;
          color: #00ff88;
          font-size: 8px;
          font-weight: bold;
          letter-spacing: 0.5px;
          text-align: center;
          line-height: 1;
        }

        .signal-dot {
          position: absolute;
          top: -5px;
          right: -5px;
          width: 12px;
          height: 12px;
          background: #00ff88;
          border-radius: 50%;
          animation: blink 1s infinite;
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
          }
          50% {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
          }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
};
