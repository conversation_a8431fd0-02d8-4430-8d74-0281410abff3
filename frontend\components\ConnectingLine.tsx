import React from 'react';

interface ConnectingLineProps {
  height?: number;
  color?: string;
  animated?: boolean;
}

export const ConnectingLine: React.FC<ConnectingLineProps> = ({ 
  height = 60, 
  color = '#00d4ff', 
  animated = true 
}) => {
  return (
    <div className="flex justify-center">
      <svg width="2" height={height} className="overflow-visible">
        <defs>
          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.8" />
            <stop offset="100%" stopColor={color} stopOpacity="0.3" />
          </linearGradient>
          {animated && (
            <linearGradient id="animatedGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity="1">
                <animate attributeName="stop-opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" />
              </stop>
              <stop offset="50%" stopColor={color} stopOpacity="0.6">
                <animate attributeName="stop-opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" />
              </stop>
              <stop offset="100%" stopColor={color} stopOpacity="0.3">
                <animate attributeName="stop-opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" />
              </stop>
            </linearGradient>
          )}
        </defs>
        <line 
          x1="1" 
          y1="0" 
          x2="1" 
          y2={height} 
          stroke={animated ? "url(#animatedGradient)" : "url(#lineGradient)"} 
          strokeWidth="2"
          className="drop-shadow-sm"
        />
      </svg>
    </div>
  );
};
