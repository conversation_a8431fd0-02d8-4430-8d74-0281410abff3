import React from 'react';

export const BuySignalIcon: React.FC = () => {
  return (
    <div className="buy-signal-icon">
      <div className="arrow-up"></div>
      <div className="buy-text">BUY<br />SIGNAL</div>

      <style jsx>{`
        .buy-signal-icon {
          width: 80px;
          height: 80px;
          border: 3px solid #00ff88;
          border-radius: 12px;
          background: rgba(0, 255, 136, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .arrow-up {
          width: 0;
          height: 0;
          border-left: 15px solid transparent;
          border-right: 15px solid transparent;
          border-bottom: 20px solid #00ff88;
          margin-bottom: 5px;
        }

        .buy-text {
          position: absolute;
          bottom: 8px;
          color: #00ff88;
          font-size: 8px;
          font-weight: bold;
          letter-spacing: 0.5px;
          text-align: center;
          line-height: 1;
        }


      `}</style>
    </div>
  );
};
