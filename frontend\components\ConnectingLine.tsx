import React from 'react';

interface ConnectingLineProps {
  height?: number;
  color?: string;
  animated?: boolean;
}

export const ConnectingLine: React.FC<ConnectingLineProps> = ({
  height = 60,
  color = '#00d4ff',
  animated = true
}) => {
  return (
    <div className="flex justify-center w-full">
      <div
        className={`w-0.5 bg-gradient-to-b from-[${color}] to-transparent ${animated ? 'animate-pulse' : ''}`}
        style={{
          height: `${height}px`,
          background: `linear-gradient(to bottom, ${color} 0%, transparent 100%)`,
          boxShadow: `0 0 10px ${color}40`
        }}
      />
    </div>
  );
};
