import React from 'react';

export const BuySignalIcon: React.FC = () => {
  return (
    <div className="buy-signal-icon">
      <div className="arrow-up"></div>
      <div className="buy-text">BUY<br />SIGNAL</div>

      <style jsx>{`
        .buy-signal-icon {
          width: 60px;
          height: 60px;
          border: 2px solid #00ff88;
          border-radius: 8px;
          background: rgba(0, 255, 136, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
        }

        .arrow-up {
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 14px solid #00ff88;
          margin-bottom: 3px;
        }

        .buy-text {
          position: absolute;
          bottom: 6px;
          color: #00ff88;
          font-size: 6px;
          font-weight: bold;
          letter-spacing: 0.3px;
          text-align: center;
          line-height: 1;
        }


      `}</style>
    </div>
  );
};
